/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  // Removed swcMinify as it's now the default in Next.js 15+
  serverExternalPackages: ['tesseract.js', '@mistralai/mistralai'], // Moved from experimental.serverComponentsExternalPackages

  // Include WASM and other binary files for Tesseract.js in serverless deployment
  outputFileTracingIncludes: {
    '/api/**/*': ['./node_modules/**/*.wasm', './node_modules/**/*.proto', './node_modules/tesseract.js-core/**/*']
  },

  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        path: false,
        os: false,
      };
    }

    // Add WASM support for Tesseract.js
    config.experiments = {
      ...config.experiments,
      asyncWebAssembly: true,
    };

    // Handle WASM files
    config.module.rules.push({
      test: /\.wasm$/,
      type: 'webassembly/async',
    });

    return config;
  },
  // Removed invalid 'api' configuration - this should be handled in individual API routes
}

module.exports = nextConfig
