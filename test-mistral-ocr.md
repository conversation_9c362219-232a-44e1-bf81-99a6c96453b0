# Тестирование Mistral OCR интеграции

## Чек-лист для тестирования

### 1. Проверка API endpoints

#### Tesseract OCR (существующий)
- [ ] `/api/ocr` - работает с изображениями
- [ ] `/api/ocr` - работает с PDF файлами
- [ ] Извлечение данных паспорта корректно
- [ ] Сохранение в Supabase работает

#### Mistral OCR (новый)
- [ ] `/api/ocr-mistral?engine=mistral` - работает с изображениями
- [ ] `/api/ocr-mistral?engine=mistral` - работает с PDF файлами
- [ ] `/api/ocr-mistral?engine=hybrid` - гибридный режим работает
- [ ] Возвращает confidence score
- [ ] Возвращает processing time
- [ ] Логирование в базу данных работает

### 2. Проверка UI компонентов

#### OCREngineSelector
- [ ] Отображает все доступные движки
- [ ] Переключение между движками работает
- [ ] Показывает предупреждения для Mistral
- [ ] Показывает информацию о гибридном режиме

#### FileUpload (обновленный)
- [ ] Показывает селектор OCR движка
- [ ] Отправляет запросы на правильные endpoints
- [ ] Отображает информацию о обработке (движок, точность, время)
- [ ] Обрабатывает ошибки корректно

#### OCRAnalytics
- [ ] Отображает статистику использования
- [ ] Показывает рекомендации
- [ ] Обновляется в реальном времени

### 3. Проверка функциональности

#### Базовая функциональность
- [ ] Загрузка документов работает
- [ ] Извлечение данных корректно
- [ ] Автозаполнение формы работает
- [ ] Сохранение в localStorage работает

#### Mistral OCR специфично
- [ ] API ключ Mistral настроен
- [ ] Обработка изображений через Mistral работает
- [ ] Гибридный режим выбирает лучший результат
- [ ] Fallback на Tesseract при ошибках Mistral

#### Производительность
- [ ] Tesseract: ~2-5 секунд
- [ ] Mistral: ~5-15 секунд
- [ ] Hybrid: ~10-20 секунд
- [ ] Timeout настроен корректно (60-90 секунд)

### 4. Проверка данных

#### Извлечение полей
- [ ] Фамилия (surname)
- [ ] Имя (name)
- [ ] Дата рождения (dateOfBirth)
- [ ] Гражданство (citizenship)
- [ ] Номер паспорта (passportNumber)
- [ ] Дата выдачи (passportIssueDate)
- [ ] Дата окончания (passportExpiryDate)
- [ ] ИИН (iin)
- [ ] Номер удостоверения (idNumber)
- [ ] Пол (gender)

#### Качество данных
- [ ] Даты в правильном формате
- [ ] Номера без лишних символов
- [ ] Имена корректно распознаны
- [ ] Confidence score адекватный (>0.7 для хороших документов)

### 5. Проверка ошибок

#### Обработка ошибок
- [ ] Неподдерживаемые форматы файлов
- [ ] Слишком большие файлы
- [ ] Поврежденные файлы
- [ ] Отсутствие API ключа Mistral
- [ ] Timeout при обработке
- [ ] Сетевые ошибки

#### Fallback механизмы
- [ ] Mistral -> Tesseract при ошибке API
- [ ] Hybrid выбирает лучший результат
- [ ] Graceful degradation при недоступности движков

## Тестовые сценарии

### Сценарий 1: Обычная загрузка паспорта
1. Выберите "Гибридный режим"
2. Загрузите четкое фото паспорта
3. Проверьте автозаполнение полей
4. Убедитесь, что confidence > 0.8

### Сценарий 2: Сравнение движков
1. Загрузите один документ с Tesseract
2. Загрузите тот же документ с Mistral
3. Загрузите тот же документ в гибридном режиме
4. Сравните результаты и время обработки

### Сценарий 3: Плохое качество документа
1. Загрузите размытое/темное фото
2. Проверьте, что гибридный режим выбирает лучший результат
3. Убедитесь, что confidence отражает качество

### Сценарий 4: Обработка ошибок
1. Попробуйте загрузить неподдерживаемый формат
2. Загрузите слишком большой файл
3. Проверьте отображение ошибок

## Команды для тестирования

### Локальное тестирование
```bash
# Запуск в режиме разработки
npm run dev

# Проверка типов
npm run type-check

# Линтинг
npm run lint
```

### Тестирование API
```bash
# Тест Tesseract endpoint
curl -X POST http://localhost:3000/api/ocr \
  -F "passport=@test-passport.jpg"

# Тест Mistral endpoint
curl -X POST "http://localhost:3000/api/ocr-mistral?engine=mistral" \
  -F "passport=@test-passport.jpg"

# Тест гибридного режима
curl -X POST "http://localhost:3000/api/ocr-mistral?engine=hybrid" \
  -F "passport=@test-passport.jpg"
```

## Критерии успеха

### Функциональность
- ✅ Все OCR движки работают
- ✅ Гибридный режим выбирает лучший результат
- ✅ UI интуитивно понятен
- ✅ Ошибки обрабатываются корректно

### Производительность
- ✅ Tesseract: < 10 секунд
- ✅ Mistral: < 30 секунд
- ✅ Hybrid: < 45 секунд
- ✅ Нет memory leaks

### Качество
- ✅ Точность Tesseract: > 70%
- ✅ Точность Mistral: > 85%
- ✅ Точность Hybrid: > 90%
- ✅ Confidence scores адекватны

### UX
- ✅ Понятный выбор движка
- ✅ Информативная обратная связь
- ✅ Быстрая обработка простых документов
- ✅ Graceful fallback при ошибках
