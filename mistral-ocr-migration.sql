-- Migration to add Mistral OCR support to existing tables
-- Run this SQL in your Supabase SQL editor

-- Add new columns to ocr_processing_history table for Mistral OCR support
ALTER TABLE ocr_processing_history 
ADD COLUMN IF NOT EXISTS ocr_engine VARCHAR(20) DEFAULT 'tesseract',
ADD COLUMN IF NOT EXISTS selected_engine VARCHAR(20),
ADD COLUMN IF NOT EXISTS confidence_score DECIMAL(3,2),
ADD COLUMN IF NOT EXISTS processing_time_ms INTEGER;

-- Add comments to new columns
COMMENT ON COLUMN ocr_processing_history.ocr_engine IS 'OCR engine used: tesseract, mistral, or hybrid';
COMMENT ON COLUMN ocr_processing_history.selected_engine IS 'Engine selected by hybrid mode (if applicable)';
COMMENT ON COLUMN ocr_processing_history.confidence_score IS 'Confidence score from 0.0 to 1.0';
COMMENT ON COLUMN ocr_processing_history.processing_time_ms IS 'Processing time in milliseconds';

-- Create index for better query performance
CREATE INDEX IF NOT EXISTS idx_ocr_processing_history_engine 
ON ocr_processing_history(ocr_engine);

CREATE INDEX IF NOT EXISTS idx_ocr_processing_history_confidence 
ON ocr_processing_history(confidence_score);

-- Update existing records to have default engine as 'tesseract'
UPDATE ocr_processing_history 
SET ocr_engine = 'tesseract' 
WHERE ocr_engine IS NULL;

-- Create a view for OCR analytics
CREATE OR REPLACE VIEW ocr_analytics AS
SELECT 
    ocr_engine,
    selected_engine,
    COUNT(*) as total_processed,
    COUNT(CASE WHEN processing_status = 'success' THEN 1 END) as successful_processed,
    ROUND(
        COUNT(CASE WHEN processing_status = 'success' THEN 1 END)::DECIMAL / COUNT(*) * 100, 
        2
    ) as success_rate_percent,
    AVG(confidence_score) as avg_confidence,
    AVG(processing_time_ms) as avg_processing_time_ms,
    MIN(created_at) as first_processed,
    MAX(created_at) as last_processed
FROM ocr_processing_history 
GROUP BY ocr_engine, selected_engine
ORDER BY total_processed DESC;

-- Grant permissions to view
GRANT SELECT ON ocr_analytics TO authenticated;

-- Create function to get OCR engine recommendations based on historical performance
CREATE OR REPLACE FUNCTION get_ocr_engine_recommendation()
RETURNS TABLE (
    recommended_engine VARCHAR(20),
    reason TEXT,
    success_rate DECIMAL,
    avg_confidence DECIMAL,
    avg_processing_time INTEGER
) 
LANGUAGE plpgsql
AS $$
BEGIN
    RETURN QUERY
    WITH engine_stats AS (
        SELECT 
            ocr_engine,
            COUNT(*) as total_count,
            COUNT(CASE WHEN processing_status = 'success' THEN 1 END)::DECIMAL / COUNT(*) as success_rate,
            AVG(confidence_score) as avg_confidence,
            AVG(processing_time_ms)::INTEGER as avg_time
        FROM ocr_processing_history 
        WHERE created_at >= NOW() - INTERVAL '30 days'
        GROUP BY ocr_engine
        HAVING COUNT(*) >= 5  -- Only consider engines with at least 5 samples
    ),
    ranked_engines AS (
        SELECT 
            ocr_engine,
            success_rate,
            avg_confidence,
            avg_time,
            ROW_NUMBER() OVER (
                ORDER BY 
                    success_rate DESC, 
                    avg_confidence DESC, 
                    avg_time ASC
            ) as rank
        FROM engine_stats
    )
    SELECT 
        re.ocr_engine,
        CASE 
            WHEN re.rank = 1 THEN 'Best overall performance based on success rate and confidence'
            WHEN re.ocr_engine = 'hybrid' THEN 'Hybrid mode provides balanced results'
            WHEN re.ocr_engine = 'mistral' THEN 'AI-powered OCR with high accuracy'
            ELSE 'Traditional OCR engine'
        END,
        re.success_rate,
        re.avg_confidence,
        re.avg_time
    FROM ranked_engines re
    WHERE re.rank = 1;
END;
$$;

-- Grant execute permission to function
GRANT EXECUTE ON FUNCTION get_ocr_engine_recommendation() TO authenticated;
