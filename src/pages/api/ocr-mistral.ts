import { NextApiRequest, NextApiResponse } from 'next';
import { IncomingForm, Fields } from 'formidable';
import fs from 'fs';
import path from 'path';
import { extractTextWithMistral, extractTextHybrid, OCREngine } from '../../utils/mistral-ocr';
import { supabase } from '../../utils/supabase';
import { v4 as uuidv4 } from 'uuid';
import { uploadDocumentToStorage } from '../../utils/supabase-storage';
import { OCRProcessingResult } from '../../utils/types';

export const config = {
  api: {
    bodyParser: false,
    responseLimit: false,
  },
  runtime: 'nodejs',
  maxDuration: 60, // Increased timeout for Mistral API calls
};

type ProcessedFiles = {
  [key: string]: {
    filepath: string;
    originalFilename?: string;
    mimetype?: string;
    size?: number;
  };
};

// Allowed MIME types - same as existing OCR endpoint
const ALLOWED_MIME_TYPES = [
  'image/jpeg',
  'image/png',
  'image/jpg',
  'image/bmp',
  'image/heic',
  'image/heif',
  'application/pdf',
  'application/octet-stream',
];

// Max file size in bytes (10MB)
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// Helper to get the correct file type
const getFileType = (file: ProcessedFiles[string]): string => {
  if (file.mimetype) {
    return file.mimetype;
  }

  if (file.originalFilename) {
    const extension = path.extname(file.originalFilename).toLowerCase();
    if (['.jpg', '.jpeg'].includes(extension)) {
      return 'image/jpeg';
    } else if (extension === '.png') {
      return 'image/png';
    } else if (extension === '.pdf') {
      return 'application/pdf';
    } else if (['.heic', '.heif'].includes(extension)) {
      return 'image/heic';
    } else if (extension === '.bmp') {
      return 'image/bmp';
    }
  }

  return 'image/jpeg';
};

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  // Set CORS headers
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  console.log('=== MISTRAL OCR API HANDLER CALLED ===');
  console.log('Method:', req.method);
  console.log('Query params:', req.query);

  if (req.method !== 'POST') {
    return res.status(405).json({
      success: false,
      error: 'Method not allowed',
      message: `Method ${req.method} not allowed. Only POST is supported.`
    });
  }

  const startTime = Date.now();

  try {
    // Get OCR engine from query params
    const engineParam = req.query.engine as string;
    const ocrEngine: OCREngine = ['tesseract', 'mistral', 'hybrid'].includes(engineParam) 
      ? engineParam as OCREngine 
      : 'mistral'; // Default to Mistral

    console.log('Using OCR engine:', ocrEngine);

    // Setup upload directory
    const uploadDir = process.env.VERCEL ? '/tmp' : path.join(process.cwd(), 'tmp');
    
    if (!fs.existsSync(uploadDir)) {
      fs.mkdirSync(uploadDir, { recursive: true });
    }

    // Create form parser
    const form = new IncomingForm({
      keepExtensions: true,
      multiples: false,
      maxFileSize: MAX_FILE_SIZE,
      allowEmptyFiles: false,
      uploadDir: uploadDir,
      filename: (_name, _ext, part) => {
        const uniqueFilename = `${Date.now()}-${uuidv4()}`;
        const ext = part.mimetype?.split('/').pop() || 'jpg';
        return `${uniqueFilename}.${ext}`;
      },
      hashAlgorithm: false
    });

    // Parse form data
    const formData: { fields: Fields; files: ProcessedFiles } = await new Promise((resolve, reject) => {
      form.parse(req, (err, fields, files) => {
        if (err) {
          if (err.code === 1009) {
            return reject(new Error(`File is too large. Maximum size is ${MAX_FILE_SIZE / (1024 * 1024)}MB`));
          }
          return reject(err);
        }

        const fileKeys = Object.keys(files);
        if (fileKeys.length === 0) {
          return reject(new Error('No files found in the form data'));
        }

        resolve({ fields, files: files as unknown as ProcessedFiles });
      });
    });

    // Get agent ID
    const agentIdParam = req.cookies.agentId || req.query.agentId;
    const agentId = typeof agentIdParam === 'string' ? agentIdParam :
                    Array.isArray(agentIdParam) ? agentIdParam[0] :
                    uuidv4();

    // Extract file
    const fileKey = Object.keys(formData.files)[0];
    if (!fileKey) {
      return res.status(400).json({
        success: false,
        error: 'No file uploaded'
      });
    }

    let file = formData.files[fileKey];
    if (Array.isArray(file)) {
      file = file[0];
    }

    // Validate file
    if (!file || !file.filepath || !fs.existsSync(file.filepath)) {
      return res.status(400).json({
        success: false,
        error: 'Invalid file upload'
      });
    }

    const stats = fs.statSync(file.filepath);
    if (!stats.isFile() || stats.size === 0) {
      return res.status(400).json({
        success: false,
        error: 'Invalid file'
      });
    }

    // Validate file type
    const fileType = getFileType(file);
    if (!ALLOWED_MIME_TYPES.includes(fileType)) {
      // Clean up temp file
      try {
        fs.unlinkSync(file.filepath);
      } catch (cleanupError) {
        console.warn('Failed to clean up temp file:', cleanupError);
      }

      return res.status(400).json({
        success: false,
        error: 'Invalid file type',
        message: `File type ${fileType} is not supported. Please upload a JPG, PNG, or PDF file.`
      });
    }

    console.log('=== PROCESSING WITH MISTRAL OCR ===');
    console.log('File:', file.originalFilename);
    console.log('Type:', fileType);
    console.log('Size:', stats.size);
    console.log('Engine:', ocrEngine);

    // Process document based on selected engine
    let result: OCRProcessingResult;

    if (ocrEngine === 'hybrid') {
      const hybridResult = await extractTextHybrid(file.filepath, fileType);
      result = {
        success: hybridResult.success,
        extractedData: hybridResult.extractedData,
        confidence: hybridResult.confidence,
        engine: 'hybrid',
        selectedEngine: hybridResult.selectedEngine,
        engines: hybridResult.engines,
        error: hybridResult.error
      };
    } else if (ocrEngine === 'mistral') {
      const mistralResult = await extractTextWithMistral(file.filepath, fileType);
      result = {
        success: mistralResult.success,
        extractedData: mistralResult.extractedData,
        confidence: mistralResult.confidence,
        engine: 'mistral',
        rawText: mistralResult.rawText,
        error: mistralResult.error
      };
    } else {
      // Fallback to tesseract (using existing logic)
      const { processDocument } = await import('../../utils/ocr');
      const extractedData = await processDocument(file.filepath);
      result = {
        success: true,
        extractedData,
        engine: 'tesseract'
      };
    }

    const processingTime = Date.now() - startTime;
    result.processingTime = processingTime;

    console.log('OCR processing completed:', {
      success: result.success,
      engine: result.engine,
      selectedEngine: result.selectedEngine,
      confidence: result.confidence,
      processingTime
    });

    // Upload file to storage if processing was successful
    let filePath = '';
    if (result.success) {
      const fileName = file.originalFilename || `document.${fileType.split('/')[1]}`;
      const uploadResult = await uploadDocumentToStorage(
        file.filepath,
        agentId,
        'passport',
        fileName,
        fileType
      );

      if (uploadResult.error) {
        console.error('Upload error:', uploadResult.error);
      } else {
        filePath = uploadResult.filePath || '';
      }
    }

    // Log to database
    const logData = {
      agent_id: agentId,
      document_type: 'passport',
      document_path: filePath || 'failed_upload',
      processing_status: result.success ? 'success' : 'error',
      extracted_data: result.extractedData || null,
      ocr_engine: result.engine,
      selected_engine: result.selectedEngine || result.engine,
      confidence_score: result.confidence || null,
      processing_time_ms: processingTime,
      error_message: result.error || null
    };

    const { error: insertError } = await supabase
      .from('ocr_processing_history')
      .insert(logData);

    if (insertError) {
      console.warn('Failed to log OCR processing:', insertError);
    }

    // Clean up temp file
    try {
      fs.unlinkSync(file.filepath);
    } catch (cleanupError) {
      console.warn('Failed to clean up temp file:', cleanupError);
    }

    // Return result
    return res.status(result.success ? 200 : 500).json({
      success: result.success,
      data: result.extractedData,
      confidence: result.confidence,
      engine: result.engine,
      selectedEngine: result.selectedEngine,
      engines: result.engines,
      filePath,
      agentId,
      processingTime,
      message: result.success ? 'Document processed successfully' : result.error
    });

  } catch (error) {
    console.error('=== MISTRAL OCR API ERROR ===');
    console.error('Error details:', error);

    const processingTime = Date.now() - startTime;

    return res.status(500).json({
      success: false,
      error: 'Failed to process document',
      message: error instanceof Error ? error.message : 'Unknown error',
      processingTime
    });
  }
};

export default handler;
