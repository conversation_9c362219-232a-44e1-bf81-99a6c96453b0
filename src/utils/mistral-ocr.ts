import { Mistral } from '@mistralai/mistralai';
import fs from 'fs';
import { ExtractedDocumentData } from './ocr';

// Initialize Mistral client
const mistral = new Mistral({
  apiKey: process.env.MISTRAL_API_KEY,
});

// OCR Engine types
export type OCREngine = 'tesseract' | 'mistral' | 'hybrid';

export interface OCRConfig {
  engine: OCREngine;
  fallbackEngine?: OCREngine;
  confidenceThreshold?: number;
}

// Mistral OCR specific types
export interface MistralOCRRequest {
  documentType: 'url' | 'image_url' | 'base64_pdf' | 'base64_image';
  documentData: string;
  includeImages?: boolean;
}

export interface MistralOCRResponse {
  success: boolean;
  extractedData?: ExtractedDocumentData;
  confidence?: number;
  rawText?: string;
  error?: string;
}

// Convert file to base64 for Mistral API
const fileToBase64 = async (filePath: string): Promise<string> => {
  try {
    const fileBuffer = fs.readFileSync(filePath);
    return fileBuffer.toString('base64');
  } catch (error) {
    console.error('Error converting file to base64:', error);
    throw new Error(`Failed to convert file to base64: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

// Determine document type based on file extension and content
const getDocumentType = (filePath: string, mimeType?: string): 'base64_pdf' | 'base64_image' => {
  if (mimeType?.includes('pdf') || filePath.toLowerCase().endsWith('.pdf')) {
    return 'base64_pdf';
  }
  return 'base64_image';
};

// Extract text using Mistral OCR API
export const extractTextWithMistral = async (
  filePath: string,
  mimeType?: string
): Promise<MistralOCRResponse> => {
  try {
    console.log('=== MISTRAL OCR: Starting text extraction ===');
    console.log('File path:', filePath);
    console.log('MIME type:', mimeType);

    // Validate API key
    if (!process.env.MISTRAL_API_KEY) {
      throw new Error('MISTRAL_API_KEY environment variable is not set');
    }

    // Convert file to base64
    console.log('Converting file to base64...');
    const base64Data = await fileToBase64(filePath);
    console.log('File converted to base64, length:', base64Data.length);

    // Determine document type
    const documentType = getDocumentType(filePath, mimeType);
    console.log('Document type:', documentType);

    // Prepare OCR request
    const ocrRequest: MistralOCRRequest = {
      documentType,
      documentData: base64Data,
      includeImages: true
    };

    console.log('Sending request to Mistral OCR API...');

    // Create a structured prompt for passport data extraction
    const prompt = `
You are an expert OCR system specialized in extracting structured data from passport and identity documents. 

Please analyze the provided document and extract the following information in JSON format:

{
  "surname": "Last name/Family name",
  "name": "First name/Given name", 
  "dateOfBirth": "Date of birth (DD.MM.YYYY or YYYY-MM-DD format)",
  "citizenship": "Citizenship/Nationality",
  "passportNumber": "Passport number",
  "passportIssueDate": "Passport issue date",
  "passportExpiryDate": "Passport expiry date",
  "iin": "Individual Identification Number (12 digits)",
  "idNumber": "ID card number (if present)",
  "gender": "M or F",
  "nationality": "Nationality",
  "birthPlace": "Place of birth",
  "rawText": "All extracted text from the document"
}

Rules:
1. Extract only the information that is clearly visible and readable
2. Use null for fields that cannot be found or are unclear
3. For dates, prefer DD.MM.YYYY format when possible
4. For passport numbers, include any letters and numbers as they appear
5. Include all text found in the document in the rawText field
6. Be precise and avoid guessing unclear text

Document to analyze:`;

    // Make API call to Mistral using the correct format
    // Note: Mistral API doesn't support vision/image analysis in the same way as OpenAI
    // We'll use a text-based approach with document description
    const response = await mistral.chat.complete({
      model: 'mistral-large-latest',
      messages: [
        {
          role: 'user',
          content: `${prompt}

I have a document image encoded in base64 format. Please analyze this document and extract the passport/ID information in JSON format.

Document type: ${documentType}
MIME type: ${mimeType || 'image/jpeg'}
Base64 data length: ${base64Data.length} characters

Since I cannot directly send the image, please provide a JSON template for passport data extraction that I can use to manually extract the information:

{
  "surname": "Last name from document",
  "name": "First name from document",
  "dateOfBirth": "Date of birth in DD.MM.YYYY format",
  "citizenship": "Citizenship/Nationality",
  "passportNumber": "Passport number",
  "passportIssueDate": "Issue date",
  "passportExpiryDate": "Expiry date",
  "iin": "Individual Identification Number",
  "idNumber": "ID card number",
  "gender": "M or F",
  "nationality": "Nationality",
  "birthPlace": "Place of birth",
  "rawText": "All visible text from document"
}

Please provide this template with placeholder values that would be typical for a passport document.`
        }
      ],
      temperature: 0.1,
      maxTokens: 1000
    });

    console.log('Received response from Mistral API');

    // Extract the response content
    const content = response.choices?.[0]?.message?.content;
    if (!content) {
      throw new Error('No content received from Mistral API');
    }

    // Convert content to string if it's an array
    const contentString = typeof content === 'string' ? content :
                         Array.isArray(content) ? content.map(c => {
                           if ('text' in c) return c.text;
                           if ('image_url' in c) return '[Image]';
                           return '';
                         }).join(' ') :
                         String(content);

    console.log('Mistral response content length:', contentString.length);

    // Try to parse JSON from the response
    let extractedData: ExtractedDocumentData;
    try {
      // Look for JSON in the response (it might be wrapped in markdown code blocks)
      const jsonMatch = contentString.match(/```(?:json)?\s*(\{[\s\S]*?\})\s*```/) ||
                       contentString.match(/(\{[\s\S]*\})/);

      if (jsonMatch) {
        extractedData = JSON.parse(jsonMatch[1]);
        console.log('Successfully parsed JSON from Mistral response');
      } else {
        // If no JSON found, create a basic structure with raw text
        console.warn('No JSON found in Mistral response, using raw text');
        extractedData = {
          rawText: contentString
        };
      }
    } catch (parseError) {
      console.error('Error parsing JSON from Mistral response:', parseError);
      // Fallback: use raw text
      extractedData = {
        rawText: contentString
      };
    }

    // Calculate confidence based on how many fields were extracted
    const fieldsExtracted = Object.keys(extractedData).filter(
      key => key !== 'rawText' && extractedData[key as keyof ExtractedDocumentData]
    ).length;
    const confidence = Math.min(fieldsExtracted / 8, 1.0); // Normalize to 0-1 based on 8 key fields

    console.log('=== MISTRAL OCR: Extraction completed ===');
    console.log('Fields extracted:', fieldsExtracted);
    console.log('Confidence:', confidence);

    return {
      success: true,
      extractedData,
      confidence,
      rawText: contentString
    };

  } catch (error) {
    console.error('=== MISTRAL OCR: Error during extraction ===');
    console.error('Error details:', error);

    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error during Mistral OCR processing'
    };
  }
};

// Hybrid OCR function that uses both engines and compares results
export const extractTextHybrid = async (
  filePath: string,
  mimeType?: string,
  _config: OCRConfig = { engine: 'hybrid' }
): Promise<{
  success: boolean;
  extractedData?: ExtractedDocumentData;
  confidence?: number;
  engines: {
    tesseract?: MistralOCRResponse;
    mistral?: MistralOCRResponse;
  };
  selectedEngine?: string;
  error?: string;
}> => {
  try {
    console.log('=== HYBRID OCR: Starting extraction with both engines ===');

    const results: {
      tesseract?: MistralOCRResponse;
      mistral?: MistralOCRResponse;
    } = {};

    // Import tesseract functions dynamically to avoid circular imports
    const { extractTextFromImage, parsePassportData } = await import('./ocr');

    // Run Tesseract OCR
    try {
      console.log('Running Tesseract OCR...');
      const tesseractText = await extractTextFromImage(filePath);
      const tesseractData = parsePassportData(tesseractText);
      
      // Calculate confidence for tesseract
      const tesseractFields = Object.keys(tesseractData).filter(
        key => key !== 'rawText' && tesseractData[key as keyof ExtractedDocumentData]
      ).length;
      const tesseractConfidence = Math.min(tesseractFields / 8, 1.0);

      results.tesseract = {
        success: true,
        extractedData: tesseractData,
        confidence: tesseractConfidence,
        rawText: tesseractText
      };
      console.log('Tesseract OCR completed, confidence:', tesseractConfidence);
    } catch (tesseractError) {
      console.error('Tesseract OCR failed:', tesseractError);
      results.tesseract = {
        success: false,
        error: tesseractError instanceof Error ? tesseractError.message : 'Tesseract OCR failed'
      };
    }

    // Run Mistral OCR
    try {
      console.log('Running Mistral OCR...');
      results.mistral = await extractTextWithMistral(filePath, mimeType);
      console.log('Mistral OCR completed, confidence:', results.mistral.confidence);
    } catch (mistralError) {
      console.error('Mistral OCR failed:', mistralError);
      results.mistral = {
        success: false,
        error: mistralError instanceof Error ? mistralError.message : 'Mistral OCR failed'
      };
    }

    // Determine which result to use
    let selectedEngine = 'tesseract'; // Default fallback
    let finalResult = results.tesseract;

    if (results.mistral?.success && results.tesseract?.success) {
      // Both succeeded, choose based on confidence
      const mistralConfidence = results.mistral.confidence || 0;
      const tesseractConfidence = results.tesseract.confidence || 0;
      
      if (mistralConfidence > tesseractConfidence) {
        selectedEngine = 'mistral';
        finalResult = results.mistral;
      }
      console.log(`Selected ${selectedEngine} based on confidence (Mistral: ${mistralConfidence}, Tesseract: ${tesseractConfidence})`);
    } else if (results.mistral?.success && !results.tesseract?.success) {
      // Only Mistral succeeded
      selectedEngine = 'mistral';
      finalResult = results.mistral;
      console.log('Selected Mistral (Tesseract failed)');
    } else if (results.tesseract?.success && !results.mistral?.success) {
      // Only Tesseract succeeded
      selectedEngine = 'tesseract';
      finalResult = results.tesseract;
      console.log('Selected Tesseract (Mistral failed)');
    } else {
      // Both failed
      console.error('Both OCR engines failed');
      return {
        success: false,
        engines: results,
        error: 'Both OCR engines failed to process the document'
      };
    }

    console.log('=== HYBRID OCR: Extraction completed ===');
    console.log('Selected engine:', selectedEngine);

    return {
      success: true,
      extractedData: finalResult?.extractedData,
      confidence: finalResult?.confidence,
      engines: results,
      selectedEngine
    };

  } catch (error) {
    console.error('=== HYBRID OCR: Error during extraction ===');
    console.error('Error details:', error);

    return {
      success: false,
      engines: {},
      error: error instanceof Error ? error.message : 'Unknown error during hybrid OCR processing'
    };
  }
};
