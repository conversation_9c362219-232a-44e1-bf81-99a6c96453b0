import { OCREngine } from './types';

// OCR Configuration Management
export interface OCRSettings {
  defaultEngine: OCREngine;
  enableHybridMode: boolean;
  confidenceThreshold: number;
  maxRetries: number;
  timeoutMs: number;
  enableAnalytics: boolean;
}

// Default OCR settings
const DEFAULT_OCR_SETTINGS: OCRSettings = {
  defaultEngine: 'hybrid',
  enableHybridMode: true,
  confidenceThreshold: 0.7,
  maxRetries: 2,
  timeoutMs: 60000, // 60 seconds
  enableAnalytics: true
};

// Local storage key for OCR settings
const OCR_SETTINGS_KEY = 'ocr_settings';

// Get OCR settings from localStorage with fallback to defaults
export const getOCRSettings = (): OCRSettings => {
  if (typeof window === 'undefined') {
    return DEFAULT_OCR_SETTINGS;
  }

  try {
    const stored = localStorage.getItem(OCR_SETTINGS_KEY);
    if (stored) {
      const parsed = JSON.parse(stored);
      return { ...DEFAULT_OCR_SETTINGS, ...parsed };
    }
  } catch (error) {
    console.warn('Failed to parse OCR settings from localStorage:', error);
  }

  return DEFAULT_OCR_SETTINGS;
};

// Save OCR settings to localStorage
export const saveOCRSettings = (settings: Partial<OCRSettings>): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const currentSettings = getOCRSettings();
    const newSettings = { ...currentSettings, ...settings };
    localStorage.setItem(OCR_SETTINGS_KEY, JSON.stringify(newSettings));
  } catch (error) {
    console.error('Failed to save OCR settings to localStorage:', error);
  }
};

// Reset OCR settings to defaults
export const resetOCRSettings = (): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    localStorage.removeItem(OCR_SETTINGS_KEY);
  } catch (error) {
    console.error('Failed to reset OCR settings:', error);
  }
};

// Get recommended OCR engine based on document type and user preferences
export const getRecommendedEngine = (
  documentType: 'passport' | 'id' | 'general' = 'passport',
  userPreference?: OCREngine
): OCREngine => {
  const settings = getOCRSettings();

  // If user has a specific preference, use it
  if (userPreference) {
    return userPreference;
  }

  // Use default engine from settings
  if (settings.defaultEngine) {
    return settings.defaultEngine;
  }

  // Fallback recommendations based on document type
  switch (documentType) {
    case 'passport':
      return 'hybrid'; // Best for passport documents
    case 'id':
      return 'mistral'; // AI works well for ID cards
    case 'general':
      return 'tesseract'; // Fast for general documents
    default:
      return 'hybrid';
  }
};

// OCR Engine capabilities and characteristics
export const OCR_ENGINE_INFO = {
  tesseract: {
    name: 'Tesseract OCR',
    description: 'Traditional OCR engine, fast and reliable',
    pros: ['Fast processing', 'Works offline', 'Good for clear text'],
    cons: ['Lower accuracy on complex layouts', 'Struggles with handwriting'],
    avgProcessingTime: 2000, // ms
    avgAccuracy: 0.75,
    supportedLanguages: ['eng', 'rus', 'kaz'],
    costPerDocument: 0, // Free
  },
  mistral: {
    name: 'Mistral AI OCR',
    description: 'AI-powered OCR with advanced understanding',
    pros: ['High accuracy', 'Handles complex layouts', 'Context awareness'],
    cons: ['Requires internet', 'Slower processing', 'API costs'],
    avgProcessingTime: 8000, // ms
    avgAccuracy: 0.92,
    supportedLanguages: ['eng', 'rus', 'kaz', 'multi'],
    costPerDocument: 0.01, // Estimated cost in USD
  },
  hybrid: {
    name: 'Hybrid Mode',
    description: 'Uses both engines and selects the best result',
    pros: ['Best accuracy', 'Automatic fallback', 'Confidence scoring'],
    cons: ['Longer processing time', 'Higher resource usage'],
    avgProcessingTime: 10000, // ms
    avgAccuracy: 0.95,
    supportedLanguages: ['eng', 'rus', 'kaz', 'multi'],
    costPerDocument: 0.005, // Average cost
  }
} as const;

// Get engine information
export const getEngineInfo = (engine: OCREngine) => {
  return OCR_ENGINE_INFO[engine];
};

// Calculate estimated processing cost
export const calculateProcessingCost = (
  engine: OCREngine,
  documentCount: number = 1
): number => {
  const engineInfo = getEngineInfo(engine);
  return engineInfo.costPerDocument * documentCount;
};

// Get processing time estimate
export const getProcessingTimeEstimate = (
  engine: OCREngine,
  documentCount: number = 1
): number => {
  const engineInfo = getEngineInfo(engine);
  return engineInfo.avgProcessingTime * documentCount;
};

// Validate OCR engine availability
export const isEngineAvailable = async (engine: OCREngine): Promise<boolean> => {
  switch (engine) {
    case 'tesseract':
      // Tesseract is always available (bundled)
      return true;
    
    case 'mistral':
      // Check if Mistral API key is configured
      return !!process.env.MISTRAL_API_KEY || !!process.env.NEXT_PUBLIC_MISTRAL_API_KEY;
    
    case 'hybrid':
      // Hybrid requires both engines
      return true; // Tesseract is always available, Mistral will fallback gracefully
    
    default:
      return false;
  }
};

// Get available engines
export const getAvailableEngines = async (): Promise<OCREngine[]> => {
  const engines: OCREngine[] = [];
  
  if (await isEngineAvailable('tesseract')) {
    engines.push('tesseract');
  }
  
  if (await isEngineAvailable('mistral')) {
    engines.push('mistral');
  }
  
  if (await isEngineAvailable('hybrid')) {
    engines.push('hybrid');
  }
  
  return engines;
};

// OCR Analytics helpers
export interface OCRAnalytics {
  totalProcessed: number;
  successRate: number;
  avgConfidence: number;
  avgProcessingTime: number;
  engineUsage: Record<OCREngine, number>;
  lastProcessed?: Date;
}

// Get OCR analytics from localStorage
export const getOCRAnalytics = (): OCRAnalytics => {
  if (typeof window === 'undefined') {
    return {
      totalProcessed: 0,
      successRate: 0,
      avgConfidence: 0,
      avgProcessingTime: 0,
      engineUsage: { tesseract: 0, mistral: 0, hybrid: 0 }
    };
  }

  try {
    const stored = localStorage.getItem('ocr_analytics');
    if (stored) {
      const parsed = JSON.parse(stored);
      return {
        totalProcessed: parsed.totalProcessed || 0,
        successRate: parsed.successRate || 0,
        avgConfidence: parsed.avgConfidence || 0,
        avgProcessingTime: parsed.avgProcessingTime || 0,
        engineUsage: parsed.engineUsage || { tesseract: 0, mistral: 0, hybrid: 0 },
        lastProcessed: parsed.lastProcessed ? new Date(parsed.lastProcessed) : undefined
      };
    }
  } catch (error) {
    console.warn('Failed to parse OCR analytics from localStorage:', error);
  }

  return {
    totalProcessed: 0,
    successRate: 0,
    avgConfidence: 0,
    avgProcessingTime: 0,
    engineUsage: { tesseract: 0, mistral: 0, hybrid: 0 }
  };
};

// Update OCR analytics
export const updateOCRAnalytics = (
  engine: OCREngine,
  success: boolean,
  confidence?: number,
  processingTime?: number
): void => {
  if (typeof window === 'undefined') {
    return;
  }

  try {
    const analytics = getOCRAnalytics();
    
    // Update counters
    analytics.totalProcessed += 1;
    analytics.engineUsage[engine] += 1;
    analytics.lastProcessed = new Date();
    
    // Update success rate
    const previousSuccesses = Math.round(analytics.successRate * (analytics.totalProcessed - 1));
    const newSuccesses = previousSuccesses + (success ? 1 : 0);
    analytics.successRate = newSuccesses / analytics.totalProcessed;
    
    // Update average confidence
    if (confidence !== undefined) {
      const previousTotal = analytics.avgConfidence * (analytics.totalProcessed - 1);
      analytics.avgConfidence = (previousTotal + confidence) / analytics.totalProcessed;
    }
    
    // Update average processing time
    if (processingTime !== undefined) {
      const previousTotal = analytics.avgProcessingTime * (analytics.totalProcessed - 1);
      analytics.avgProcessingTime = (previousTotal + processingTime) / analytics.totalProcessed;
    }
    
    localStorage.setItem('ocr_analytics', JSON.stringify(analytics));
  } catch (error) {
    console.error('Failed to update OCR analytics:', error);
  }
};
