import React from 'react';
import { OCREngine } from '../utils/types';

interface OCREngineSelectorProps {
  selectedEngine: OCREngine;
  onEngineChange: (engine: OCREngine) => void;
  disabled?: boolean;
  className?: string;
}

const OCREngineSelector: React.FC<OCREngineSelectorProps> = ({
  selectedEngine,
  onEngineChange,
  disabled = false,
  className = ''
}) => {
  const engines = [
    {
      value: 'tesseract' as OCREngine,
      label: 'Tesseract OCR',
      description: 'Традиционный OCR движок (быстрый, локальный)',
      icon: '🔍'
    },
    {
      value: 'mistral' as OCREngine,
      label: 'Mistral AI OCR',
      description: 'AI-powered OCR с улучшенной точностью',
      icon: '🤖'
    },
    {
      value: 'hybrid' as OCREngine,
      label: 'Гибридный режим',
      description: 'Использует оба движка и выбирает лучший результат',
      icon: '⚡'
    }
  ];

  return (
    <div className={`space-y-3 ${className}`}>
      <label className="block text-sm font-medium text-gray-700 mb-2">
        Выберите OCR движок
      </label>
      
      <div className="grid gap-3">
        {engines.map((engine) => (
          <label
            key={engine.value}
            className={`
              relative flex items-start p-4 border rounded-lg cursor-pointer transition-all
              ${selectedEngine === engine.value
                ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200'
                : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
              }
              ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
            `}
          >
            <input
              type="radio"
              name="ocrEngine"
              value={engine.value}
              checked={selectedEngine === engine.value}
              onChange={(e) => onEngineChange(e.target.value as OCREngine)}
              disabled={disabled}
              className="sr-only"
            />
            
            <div className="flex items-start space-x-3 w-full">
              <span className="text-2xl flex-shrink-0 mt-1">
                {engine.icon}
              </span>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center space-x-2">
                  <span className={`
                    font-medium text-sm
                    ${selectedEngine === engine.value ? 'text-blue-900' : 'text-gray-900'}
                  `}>
                    {engine.label}
                  </span>
                  
                  {engine.value === 'mistral' && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800">
                      AI
                    </span>
                  )}
                  
                  {engine.value === 'hybrid' && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                      Рекомендуется
                    </span>
                  )}
                </div>
                
                <p className={`
                  text-xs mt-1
                  ${selectedEngine === engine.value ? 'text-blue-700' : 'text-gray-500'}
                `}>
                  {engine.description}
                </p>
              </div>
              
              {selectedEngine === engine.value && (
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
              )}
            </div>
          </label>
        ))}
      </div>
      
      {selectedEngine === 'mistral' && (
        <div className="mt-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-yellow-700">
                <strong>Примечание:</strong> Mistral AI OCR требует интернет-соединения и может занять больше времени для обработки.
              </p>
            </div>
          </div>
        </div>
      )}
      
      {selectedEngine === 'hybrid' && (
        <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-md">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-blue-700">
                <strong>Гибридный режим:</strong> Использует оба OCR движка параллельно и автоматически выбирает результат с наивысшей точностью.
              </p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default OCREngineSelector;
