import React, { useState, useEffect } from 'react';
import { getOCRAnalytics, OCRAnalytics, getEngineInfo } from '../utils/ocr-config';
import { OCREngine } from '../utils/types';

interface OCRAnalyticsProps {
  className?: string;
  showDetailed?: boolean;
}

const OCRAnalyticsComponent: React.FC<OCRAnalyticsProps> = ({
  className = '',
  showDetailed = false
}) => {
  const [analytics, setAnalytics] = useState<OCRAnalytics | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadAnalytics = () => {
      try {
        const data = getOCRAnalytics();
        setAnalytics(data);
      } catch (error) {
        console.error('Failed to load OCR analytics:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadAnalytics();

    // Refresh analytics every 30 seconds
    const interval = setInterval(loadAnalytics, 30000);
    return () => clearInterval(interval);
  }, []);

  if (isLoading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
        <div className="h-4 bg-gray-200 rounded w-1/2"></div>
      </div>
    );
  }

  if (!analytics || analytics.totalProcessed === 0) {
    return (
      <div className={`text-gray-500 text-sm ${className}`}>
        <p>📊 Статистика OCR будет доступна после обработки документов</p>
      </div>
    );
  }

  const getMostUsedEngine = (): { engine: OCREngine; count: number } => {
    let maxEngine: OCREngine = 'tesseract';
    let maxCount = 0;

    Object.entries(analytics.engineUsage).forEach(([engine, count]) => {
      if (count > maxCount) {
        maxEngine = engine as OCREngine;
        maxCount = count;
      }
    });

    return { engine: maxEngine, count: maxCount };
  };

  const mostUsed = getMostUsedEngine();
  const successRatePercent = (analytics.successRate * 100).toFixed(1);
  const avgConfidencePercent = (analytics.avgConfidence * 100).toFixed(1);
  const avgTimeSeconds = (analytics.avgProcessingTime / 1000).toFixed(1);

  if (!showDetailed) {
    return (
      <div className={`bg-gray-50 rounded-lg p-3 text-sm ${className}`}>
        <div className="flex items-center justify-between">
          <span className="text-gray-600">📊 OCR Статистика:</span>
          <span className="font-medium text-gray-800">
            {analytics.totalProcessed} документов
          </span>
        </div>
        <div className="flex items-center justify-between mt-1">
          <span className="text-gray-600">Успешность:</span>
          <span className={`font-medium ${
            analytics.successRate >= 0.9 ? 'text-green-600' : 
            analytics.successRate >= 0.7 ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {successRatePercent}%
          </span>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border border-gray-200 p-4 ${className}`}>
      <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
        📊 Аналитика OCR
        {analytics.lastProcessed && (
          <span className="ml-2 text-xs text-gray-500">
            (обновлено {analytics.lastProcessed.toLocaleTimeString()})
          </span>
        )}
      </h3>

      {/* Summary Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {analytics.totalProcessed}
          </div>
          <div className="text-sm text-gray-600">Документов</div>
        </div>

        <div className="text-center">
          <div className={`text-2xl font-bold ${
            analytics.successRate >= 0.9 ? 'text-green-600' : 
            analytics.successRate >= 0.7 ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {successRatePercent}%
          </div>
          <div className="text-sm text-gray-600">Успешность</div>
        </div>

        <div className="text-center">
          <div className={`text-2xl font-bold ${
            analytics.avgConfidence >= 0.8 ? 'text-green-600' : 
            analytics.avgConfidence >= 0.6 ? 'text-yellow-600' : 'text-red-600'
          }`}>
            {avgConfidencePercent}%
          </div>
          <div className="text-sm text-gray-600">Точность</div>
        </div>

        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600">
            {avgTimeSeconds}с
          </div>
          <div className="text-sm text-gray-600">Среднее время</div>
        </div>
      </div>

      {/* Engine Usage */}
      <div className="mb-6">
        <h4 className="text-md font-medium text-gray-800 mb-3">Использование движков</h4>
        <div className="space-y-3">
          {Object.entries(analytics.engineUsage).map(([engine, count]) => {
            const engineTyped = engine as OCREngine;
            const percentage = analytics.totalProcessed > 0 
              ? (count / analytics.totalProcessed * 100).toFixed(1) 
              : '0';
            const engineInfo = getEngineInfo(engineTyped);

            return (
              <div key={engine} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <span className="text-sm font-medium text-gray-700">
                    {engineInfo.name}
                  </span>
                  {mostUsed.engine === engineTyped && (
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                      Популярный
                    </span>
                  )}
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-20 bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${percentage}%` }}
                    ></div>
                  </div>
                  <span className="text-sm text-gray-600 w-12 text-right">
                    {count} ({percentage}%)
                  </span>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Recommendations */}
      <div className="bg-blue-50 rounded-lg p-3">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Рекомендации</h4>
        <div className="text-sm text-blue-800 space-y-1">
          {analytics.successRate < 0.8 && (
            <p>• Попробуйте использовать гибридный режим для повышения точности</p>
          )}
          {analytics.avgConfidence < 0.7 && (
            <p>• Убедитесь, что документы имеют хорошее качество и освещение</p>
          )}
          {analytics.avgProcessingTime > 15000 && (
            <p>• Для ускорения обработки используйте Tesseract OCR</p>
          )}
          {mostUsed.engine === 'tesseract' && analytics.avgConfidence < 0.8 && (
            <p>• Попробуйте Mistral AI для повышения точности распознавания</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default OCRAnalyticsComponent;
